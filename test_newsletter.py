#!/usr/bin/env python3
"""
Test script for newsletter subscription functionality
"""

import requests
import json
import sys

def test_newsletter_subscription():
    """Test the newsletter subscription endpoint"""
    base_url = "http://127.0.0.1:7000"
    
    print("🧪 Testing CVBioLabs Newsletter Subscription")
    print("=" * 50)
    
    # Test data
    test_email = "<EMAIL>"
    
    # Test 1: Valid email subscription
    print("1. Testing valid email subscription...")
    try:
        response = requests.post(
            f"{base_url}/subscribe-newsletter",
            json={"email": test_email},
            headers={"Content-Type": "application/json"}
        )

        print(f"   Response status: {response.status_code}")
        print(f"   Response text: {response.text}")

        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success: {data.get('message', 'Subscribed successfully')}")
        else:
            print(f"   ❌ Failed: {response.status_code} - {response.text}")

    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 2: Duplicate subscription
    print("\n2. Testing duplicate email subscription...")
    try:
        response = requests.post(
            f"{base_url}/subscribe-newsletter",
            json={"email": test_email},
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Handled correctly: {data.get('message', 'Already subscribed')}")
        else:
            print(f"   ❌ Failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 3: Invalid email format
    print("\n3. Testing invalid email format...")
    try:
        response = requests.post(
            f"{base_url}/subscribe-newsletter",
            json={"email": "invalid-email"},
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 400:
            data = response.json()
            print(f"   ✅ Validation works: {data.get('message', 'Invalid email rejected')}")
        else:
            print(f"   ❌ Should have failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 4: Missing email
    print("\n4. Testing missing email...")
    try:
        response = requests.post(
            f"{base_url}/subscribe-newsletter",
            json={},
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 400:
            data = response.json()
            print(f"   ✅ Validation works: {data.get('message', 'Missing email rejected')}")
        else:
            print(f"   ❌ Should have failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    print("\n" + "=" * 50)
    print("Newsletter subscription testing completed!")
    print("\n📧 To test the email functionality:")
    print("1. Go to http://127.0.0.1:7000")
    print("2. Scroll to the footer")
    print("3. Enter your email in the 'Stay Updated' section")
    print("4. Click the send button")
    print("5. Check your email for the confirmation message")

if __name__ == "__main__":
    test_newsletter_subscription()
