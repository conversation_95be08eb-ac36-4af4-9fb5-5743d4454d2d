-- Add newsletter subscriptions table to CVBioLabs database
USE cvbiolabs;

-- Create newsletter_subscriptions table
CREATE TABLE IF NOT EXISTS newsletter_subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    subscribed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    unsubscribe_token VARCHAR(255) UNIQUE,
    unsubscribed_at TIMESTAMP NULL,
    source VARCHAR(50) DEFAULT 'website',
    ip_address VARCHAR(45),
    user_agent TEXT,
    INDEX idx_email (email),
    INDEX idx_active (is_active),
    INDEX idx_subscribed_at (subscribed_at),
    INDEX idx_unsubscribe_token (unsubscribe_token)
);

-- Add some sample data (optional)
-- INSERT INTO newsletter_subscriptions (email, source) VALUES 
-- ('<EMAIL>', 'website'),
-- ('<EMAIL>', 'admin');

SELECT 'Newsletter subscriptions table created successfully!' as message;
