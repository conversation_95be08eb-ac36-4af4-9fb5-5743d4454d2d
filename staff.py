from flask import Flask, Blueprint, render_template, request, jsonify, send_file, redirect, url_for, send_from_directory, session, flash, current_app
from werkzeug.utils import safe_join
import mysql.connector
from mysql.connector import Error
from datetime import datetime, timedelta
import io
from openpyxl import Workbook
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet
import bcrypt
import logging
import os
from logging.handlers import RotatingFileHandler
from flask_login import login_required as staff_login_required, logout_user
from flask_wtf.csrf import generate_csrf, CSRFProtect
from flask_session import Session
import redis
from dotenv import load_dotenv
import time
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.application import MIMEApplication
import smtplib
import logging
from file_security import secure_file_handler
from functools import wraps



# Load environment variables  
load_dotenv()

# Configure logging
# Create logs directory if it doesn't exist
if not os.path.exists('logs'):
    os.makedirs('logs')

# Configure logging to file
file_handler = RotatingFileHandler('logs/app.log', maxBytes=1024*1024, backupCount=10)
file_handler.setFormatter(logging.Formatter(
    '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
))
file_handler.setLevel(logging.WARNING)

# Configure logging to console (only errors)
console_handler = logging.StreamHandler()
console_handler.setFormatter(logging.Formatter('%(levelname)s: %(message)s'))
console_handler.setLevel(logging.ERROR)

# Configure root logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.WARNING)
logger.addHandler(file_handler)
logger.addHandler(console_handler)

# Create Blueprint
staff_bp = Blueprint('staff', __name__, template_folder='templates')

# Create Flask app
app = Flask(__name__)

# Secure environment variable enforcement
app.secret_key = os.getenv('SECRET_KEY')
if not app.secret_key:
    raise RuntimeError('SECRET_KEY must be set in .env for production!')

# Import CSRF protection from main app
from flask_wtf.csrf import CSRFProtect

# Get the CSRF instance from the main app (will be set when blueprint is registered)
csrf = None

def get_csrf_instance():
    """Get the CSRF instance from the current app"""
    global csrf
    if csrf is None:
        from flask import current_app
        csrf = current_app.extensions.get('csrf')
    return csrf

redis_url = os.getenv('REDIS_URL')
if not redis_url:
    raise RuntimeError('REDIS_URL must be set in .env for production!')
app.config['SESSION_REDIS'] = redis.from_url(redis_url)

# Database configuration (no insecure defaults)
DB_CONFIG = {
    'host': os.getenv('DB_HOST'),
    'user': os.getenv('DB_USER'),
    'password': os.getenv('DB_PASSWORD'),
    'database': os.getenv('DB_NAME'),
    'charset': os.getenv('DB_CHARSET', 'utf8mb4')
}
for key in ['host', 'user', 'password', 'database']:
    if not DB_CONFIG[key]:
        raise RuntimeError(f"DB_{key.upper()} must be set in .env for production!")

# Configure Redis session
app.config['SESSION_TYPE'] = 'redis'
app.config['SESSION_COOKIE_SECURE'] = True
app.config['SESSION_COOKIE_HTTPONLY'] = True
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(days=1)
Session(app)

def get_db_connection():
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        return conn
    except Error as e:
        logger.error(f"Database connection error: {e}")
        return None

# Create necessary tables if they don't exist
def init_db():
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)
    
    # Create doctors table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS doctors (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            specialization VARCHAR(100),
            email VARCHAR(100) UNIQUE,
            phone VARCHAR(20),
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ''')

    # Create reports table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS reports (
            id INT AUTO_INCREMENT PRIMARY KEY,
            booking_id INT NOT NULL,
            patient_id INT NOT NULL,
            test_id INT NOT NULL,
            doctor_id INT,
            report_type ENUM('standard', 'detailed', 'summary') DEFAULT 'standard',
            report_status ENUM('pending', 'completed', 'reviewed') DEFAULT 'pending',
            report_format ENUM('pdf', 'excel', 'csv') DEFAULT 'pdf',
            file_path VARCHAR(255),
            doctor_review TEXT,
            comments TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (booking_id) REFERENCES bookings(id),
            FOREIGN KEY (patient_id) REFERENCES patients(id),
            FOREIGN KEY (test_id) REFERENCES tests(id),
            FOREIGN KEY (doctor_id) REFERENCES doctors(id)
        )
    ''')

    # Create pickup_agents table if it doesn't exist (using consistent schema)
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS pickup_agents (
            id INT AUTO_INCREMENT PRIMARY KEY,
            professional_id VARCHAR(10) UNIQUE,
            name VARCHAR(100) NOT NULL,
            phone VARCHAR(15) NOT NULL UNIQUE,
            status ENUM('Available', 'Busy', 'Inactive') NOT NULL DEFAULT 'Available',
            vehicle_number VARCHAR(20),
            service_area VARCHAR(100),
            created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            email VARCHAR(100) UNIQUE,
            password_hash VARCHAR(255)
        )
    ''')

    # Create sample_collections table if it doesn't exist
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS sample_collections (
            id INT AUTO_INCREMENT PRIMARY KEY,
            booking_id INT NOT NULL,
            agent_id INT,
            collection_date DATE NOT NULL,
            collection_time TIME NOT NULL,
            status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (booking_id) REFERENCES bookings(id),
            FOREIGN KEY (agent_id) REFERENCES pickup_agents(id)
        )
    ''')

    # Create patient_report table if it doesn't exist
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS patient_report (
            id INT AUTO_INCREMENT PRIMARY KEY,
            report_id INT NOT NULL,
            booking_id INT NOT NULL,
            patient_id INT NOT NULL,
            test_name VARCHAR(255) NOT NULL,
            barcode VARCHAR(255) NOT NULL,
            report_url VARCHAR(255) NOT NULL,
            report_status ENUM('Pending', 'Verified', 'Completed') DEFAULT 'Pending',
            comment TEXT,
            sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            sent_by_receptionist_id INT,
            verified_by_admin_id INT,
            FOREIGN KEY (report_id) REFERENCES reports(id) ON DELETE CASCADE,
            FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
            FOREIGN KEY (patient_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (sent_by_receptionist_id) REFERENCES admin_users(id) ON DELETE SET NULL,
            FOREIGN KEY (verified_by_admin_id) REFERENCES admin_users(id) ON DELETE SET NULL
        )
    ''')

    # Insert some sample pickup agents if the table is empty
    cursor.execute("SELECT COUNT(*) as count FROM pickup_agents")
    result = cursor.fetchone()
    if result and result['count'] == 0:
        sample_agents = [
            ('PA001', 'John Doe', '**********', '<EMAIL>', 'Available'),
            ('PA002', 'Jane Smith', '**********', '<EMAIL>', 'Available'),
            ('PA003', 'Mike Johnson', '**********', '<EMAIL>', 'Available')
        ]
        cursor.executemany('''
            INSERT INTO pickup_agents (professional_id, name, phone, email, status)
            VALUES (%s, %s, %s, %s, %s)
        ''', sample_agents)

    conn.commit()
    cursor.close()
    conn.close()

# Template filter for badge colors
@staff_bp.app_template_filter('get_badge_color')
def get_badge_color(status):
    status_colors = {
        "active": "success",
        "inactive": "secondary",
        "pending": "warning",
        "banned": "danger",
        "paid": "success",
        "failed": "danger",
        "scheduled": "light-blue",
        "completed": "success",
        "cancelled": "danger"
    }
    return status_colors.get(status.lower(), "dark")

@staff_bp.app_context_processor
def inject_csrf_token():
    return dict(csrf_token=generate_csrf())

# Add CSRF error handler for staff blueprint
@staff_bp.errorhandler(400)
def handle_csrf_error(e):
    """Handle CSRF errors specifically for staff endpoints"""
    if 'CSRF' in str(e) or 'csrf' in str(e).lower():
        logger.warning(f"CSRF error in staff module: {str(e)}")
        return jsonify({
            'error': 'CSRF token missing or invalid. Please refresh the page and try again.'
        }), 400
    return jsonify({'error': 'Bad request'}), 400

# Root route to render staff.html (Dashboard)
@staff_bp.route('/')
@staff_login_required
def dashboard():
    try:
        conn = get_db_connection()
        if not conn:
            flash('Database connection failed', 'error')
            return render_template('staff.html', 
                                total_appointments=0,
                                scheduled_pickups=0,
                                pending_reports=0)

        with conn.cursor(dictionary=True) as cursor:
            # Get total appointments for today
            cursor.execute("""
                SELECT COUNT(*) as count 
                FROM bookings 
                WHERE DATE(booking_date) = CURDATE()
            """)
            result = cursor.fetchone()
            total_appointments = result['count'] if result else 0

            # Get scheduled pickups
            cursor.execute("""
                SELECT COUNT(*) as count 
                FROM sample_collections 
                WHERE collection_status = 'Pending'
            """)
            result = cursor.fetchone()
            scheduled_pickups = result['count'] if result else 0

            # Get pending reports
            cursor.execute("""
                SELECT COUNT(*) as count 
                FROM patient_report 
                WHERE report_status = 'Pending'
            """)
            result = cursor.fetchone()
            pending_reports = result['count'] if result else 0

            # Get recent bookings
            cursor.execute("""
                SELECT b.*, t.TestName, 
                       CONCAT(u.first_name, ' ', u.last_name) as patient_name
                FROM bookings b
                JOIN testdetails t ON b.test_id = t.SrNo
                JOIN user_profiles u ON b.user_id = u.user_id
                WHERE DATE(b.booking_date) = CURDATE()
                ORDER BY b.booking_date DESC
                LIMIT 5
            """)
            recent_bookings = cursor.fetchall()

            # Get available agents
            cursor.execute("""
                SELECT COUNT(*) as count 
                FROM pickup_agents 
                WHERE status = 'Available'
            """)
            result = cursor.fetchone()
            available_agents = result['count'] if result else 0

            # Get current user info
            user_name = session.get('user_name', 'Guest')

            return render_template('staff.html',
                                total_appointments=total_appointments,
                                scheduled_pickups=scheduled_pickups,
                                pending_reports=pending_reports,
                                recent_bookings=recent_bookings,
                                available_agents=available_agents,
                                current_user={'name': user_name})

    except Exception as e:
        logger.error(f"Dashboard error: {str(e)}")
        flash('Error loading dashboard', 'error')
        return render_template('staff.html',
                            total_appointments=0,
                            scheduled_pickups=0,
                            pending_reports=0,
                            current_user={'name': 'Guest'})
    finally:
        if 'conn' in locals():
            conn.close()

# Payments Administration Route
@staff_bp.route('/admin/payments', methods=['GET', 'POST'])
@staff_login_required
def admin_payments():
    conn = get_db_connection()
    if not conn:
        return render_template('staff.html',
                             error="Database connection failed",
                             dashboard_stats={},
                             current_user={'name': 'Guest'})

    try:
        with conn.cursor() as cursor:
            thirty_days_ago = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')

            # Payment Summary (Last 30 Days)
            cursor.execute("""
                SELECT
                    COALESCE(SUM(CASE WHEN payment_status = 'paid' THEN amount ELSE 0 END), 0) as total_revenue,
                    COALESCE(SUM(CASE WHEN payment_status = 'pending' THEN amount ELSE 0 END), 0) as pending_payments,
                    COUNT(CASE WHEN payment_status = 'pending' THEN 1 END) as pending_count,
                    COALESCE(SUM(CASE WHEN payment_status = 'failed' THEN amount ELSE 0 END), 0) as failed_payments,
                    COUNT(CASE WHEN payment_status = 'failed' THEN 1 END) as failed_count,
                    COALESCE(SUM(CASE WHEN payment_status = 'refunded' THEN amount ELSE 0 END), 0) as refunds
                FROM payments 
                WHERE DATE(payment_date) >= %s
            """, (thirty_days_ago,))
            summary = cursor.fetchone()

            # Dashboard stats
            today = datetime.now().strftime('%Y-%m-%d')
            cursor.execute("SELECT COUNT(*) as total FROM bookings WHERE DATE(booking_date) = %s", (today,))
            total_appointments = cursor.fetchone()['total']

            dashboard_stats = {
                'total_appointments': total_appointments,
                'total_collections': float(summary['total_revenue']),
                'pending_payments': float(summary['pending_payments'])
            }

            # Current User from session
            user_name = session.get('user_name', 'Guest')
            current_user = {'name': user_name}

            return render_template('staff.html',
                                 dashboard_stats=dashboard_stats,
                                 total_revenue=float(summary['total_revenue']),
                                 pending_payments=float(summary['pending_payments']),
                                 pending_count=summary['pending_count'] or 0,
                                 failed_payments=float(summary['failed_payments']),
                                 failed_count=summary['failed_count'] or 0,
                                 refunds=float(summary['refunds']),
                                 current_user=current_user)

    except Exception as e:
        logger.error(f"Database Error in admin_payments: {e}")
        return render_template('staff.html',
                             error=f"Database error: {e}",
                             dashboard_stats={
                                 'total_appointments': 0,
                                 'total_collections': 0.0,
                                 'pending_payments': 0.0
                             },
                             total_revenue=0.0,
                             pending_payments=0.0,
                             pending_count=0,
                             failed_payments=0.0,
                             failed_count=0,
                             refunds=0.0,
                             current_user={'name': 'Guest'})
    finally:
        if conn:
            conn.close()

# API Routes for dynamic data loading
@staff_bp.route('/api/patients')
@staff_login_required
def get_patients():
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': 'Database connection failed'}), 500

        with conn.cursor() as cursor:
            # Get patient details with user_profiles
            cursor.execute("""
                SELECT 
                    u.id,
                    u.username,
                    u.email,
                    u.status,
                    up.first_name,
                    up.last_name,
                    up.phone,
                    DATE_FORMAT(up.date_of_birth, '%Y-%m-%d') as dob,
                    up.gender,
                    (SELECT MAX(booking_date) FROM bookings WHERE user_id = u.id) as last_visit
                FROM users u
                LEFT JOIN user_profiles up ON u.id = up.user_id
                WHERE u.status = 1
            """)
            patients = cursor.fetchall()
            
            # Get booking history for each patient
            for patient in patients:
                if patient['id'] is not None:
                    cursor.execute("""
                        SELECT 
                            b.id as booking_id,
                            t.TestName as test_name,
                            DATE_FORMAT(b.booking_date, '%%d/%%m/%%Y') as booking_date,
                            r.report_status,
                            b.booking_status,
                            b.barcode
                        FROM bookings b
                        JOIN testdetails t ON b.test_id = t.SrNo
                        LEFT JOIN reports r ON b.id = r.booking_id
                        WHERE b.user_id = %s
                        ORDER BY b.booking_date DESC
                    """, (patient['id'],))
                    patient['booking_history'] = cursor.fetchall()
                    
                    # Get payment history
                    cursor.execute("""
                        SELECT 
                            p.id as payment_id,
                            p.amount,
                            p.payment_method,
                            DATE_FORMAT(p.payment_date, '%%d/%%m/%%Y') as payment_date,
                            p.payment_status,
                            p.transaction_id
                        FROM payments p
                        JOIN bookings b ON p.booking_id = b.id
                        WHERE b.user_id = %s
                        ORDER BY p.payment_date DESC
                    """, (patient['id'],))
                    patient['payment_history'] = cursor.fetchall()
                else:
                    patient['booking_history'] = []
                    patient['payment_history'] = []
            
            return jsonify(patients)
    except Exception as e:
        logger.error(f"Error in get_patients: {e}")
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            conn.close()

@staff_bp.route('/api/patients/add', methods=['POST'])
@staff_login_required
def add_patient():
    try:
        data = request.form
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': 'Database connection failed'}), 500
        cursor = conn.cursor()
        cursor.execute("""
            INSERT INTO users (name, email, password, phone, address, role)
            VALUES (%s, %s, %s, %s, %s, 'patient')
        """, (data['fullName'], data['email'], data['password'], data['phone'], data['address']))
        conn.commit()
        cursor.close()
        conn.close()
        return jsonify({'success': True, 'message': 'Patient added successfully'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@staff_bp.route('/api/patients/update/<int:patient_id>', methods=['PUT'])
@staff_login_required
def update_patient(patient_id):
    try:
        data = request.get_json()
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': 'Database connection failed'}), 500
        cursor = conn.cursor()
        # Update phone in user_profiles
        cursor.execute("""
            UPDATE user_profiles 
            SET phone = %s
            WHERE user_id = %s
        """, (data['phone'], patient_id))
        # Update email in users
        cursor.execute("""
            UPDATE users 
            SET email = %s
            WHERE id = %s
        """, (data['email'], patient_id))
        conn.commit()
        cursor.close()
        conn.close()
        return jsonify({'success': True, 'message': 'Patient updated successfully'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@staff_bp.route('/api/patients/delete/<int:patient_id>', methods=['DELETE'])
@staff_login_required
def delete_patient(patient_id):
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': 'Database connection failed'}), 500
        cursor = conn.cursor()
        cursor.execute("DELETE FROM users WHERE id = %s AND role = 'patient'", (patient_id,))
        conn.commit()
        cursor.close()
        conn.close()
        return jsonify({'success': True, 'message': 'Patient deleted successfully'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@staff_bp.route('/api/pickups')
@staff_login_required
def get_pickups():
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': 'Database connection failed'}), 500
        cursor = conn.cursor(dictionary=True)
        cursor.execute("""
            SELECT p.*, u.name as patient_name, t.TestName as test_name, a.name as agent_name
            FROM pickups p
            JOIN bookings b ON p.booking_id = b.id
            JOIN users u ON b.user_id = u.id
            JOIN tests t ON b.test_id = t.SrNo
            LEFT JOIN users a ON p.agent_id = a.id
            ORDER BY p.collection_date DESC
        """)
        pickups = cursor.fetchall()
        cursor.close()
        conn.close()
        return jsonify(pickups)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@staff_bp.route('/api/pickups/add', methods=['POST'])
@staff_login_required
def add_pickup():
    try:
        data = request.form
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': 'Database connection failed'}), 500
        cursor = conn.cursor()
        cursor.execute("""
            INSERT INTO pickups (booking_id, collection_date, collection_status)
            VALUES (%s, %s, 'scheduled')
        """, (data['booking_id'], data['collection_date']))
        conn.commit()
        cursor.close()
        conn.close()
        return jsonify({'success': True, 'message': 'Pickup scheduled successfully'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@staff_bp.route('/api/payments')
@staff_login_required
def get_payments():
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': 'Database connection failed'}), 500

        with conn.cursor() as cursor:
            # Get payment details with user and test information
            cursor.execute("""
                SELECT 
                    p.id as payment_id,
                    p.booking_id,
                    p.amount,
                    p.payment_method,
                    p.transaction_id,
                    DATE_FORMAT(p.payment_date, '%d/%m/%Y') as payment_date,
                    p.payment_status,
                    COALESCE(CONCAT(up.first_name, ' ', up.last_name), u.username) as customer_name,
                    t.TestName as test_name
                FROM payments p
                JOIN bookings b ON p.booking_id = b.id
                JOIN users u ON b.user_id = u.id
                LEFT JOIN user_profiles up ON u.id = up.user_id
                JOIN testdetails t ON b.test_id = t.SrNo
                ORDER BY p.payment_date DESC
            """)
            payments = cursor.fetchall()
            return jsonify(payments)
    except Exception as e:
        logger.error(f"Error in get_payments: {e}")
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            conn.close()

@staff_bp.route('/api/payments/add', methods=['POST'])
@staff_login_required
def add_payment():
    try:
        data = request.form
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': 'Database connection failed'}), 500
        cursor = conn.cursor()
        cursor.execute("""
            INSERT INTO payments (booking_id, amount, payment_method, status, date)
            VALUES (%s, %s, %s, %s, CURDATE())
        """, (data['booking_id'], data['amount'], data['payment_method'], data['payment_status']))
        conn.commit()
        cursor.close()
        conn.close()
        return jsonify({'success': True, 'message': 'Payment recorded successfully'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@staff_bp.route('/api/tests')
@staff_login_required
def get_tests():
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': 'Database connection failed'}), 500

        with conn.cursor() as cursor:
            cursor.execute("""
                SELECT 
                    SrNo,
                    TestName,
                    TestID,
                    TestCode,
                    TestAmount,
                    OutsourceAmount,
                    OutsourceCenter,
                    SampleType,
                    TestCategory,
                    DepartmentName,
                    Accreditation,
                    IntegrationCode,
                    ShortText,
                    CAPTest,
                    TargetTAT,
                    VerificationStatus,
                    TargetTATHHMM,
                    active
                FROM testdetails
                ORDER BY SrNo ASC
            """)
            tests = cursor.fetchall()
            return jsonify(tests)
    except Exception as e:
        logger.error(f"Error in get_tests: {e}")
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            conn.close()

@staff_bp.route('/api/tests/add', methods=['POST'])
@staff_login_required
def add_test():
    try:
        data = request.form
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': 'Database connection failed'}), 500
        cursor = conn.cursor()
        cursor.execute("""
            INSERT INTO tests (TestName, TestID, TestCode, TestAmount, OutsourceAmount, 
                             OutsourceCenter, SampleType, TestCategory, DepartmentName, 
                             Accreditation, IntegrationCode, ShortText, CAPTest, 
                             TargetTAT, VerificationStatus, TargetTATHHMM, active)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 1)
        """, (
            data['testName'], data['testID'], data['testCode'], data['testAmount'],
            data.get('outsourceAmount'), data.get('outsourceCenter'), data['sampleType'],
            data['testCategory'], data.get('departmentName'), data.get('accreditation'),
            data.get('integrationCode'), data.get('shortText'), data.get('capTest'),
            data.get('targetTAT'), data.get('verificationStatus'), data.get('targetTATHHMM')
        ))
        conn.commit()
        cursor.close()
        conn.close()
        return jsonify({'success': True, 'message': 'Test added successfully'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@staff_bp.route('/api/tests/update/<int:sr_no>', methods=['PUT'])
@staff_login_required
def update_test(sr_no):
    try:
        data = request.get_json()
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': 'Database connection failed'}), 500
        cursor = conn.cursor()
        cursor.execute("""
            UPDATE tests 
            SET TestName = %s, TestID = %s, TestCode = %s, TestAmount = %s,
                OutsourceAmount = %s, OutsourceCenter = %s, SampleType = %s,
                TestCategory = %s, DepartmentName = %s, Accreditation = %s,
                IntegrationCode = %s, ShortText = %s, CAPTest = %s,
                TargetTAT = %s, VerificationStatus = %s, TargetTATHHMM = %s,
                active = %s
            WHERE SrNo = %s
        """, (
            data['testName'], data['testID'], data['testCode'], data['testAmount'],
            data.get('outsourceAmount'), data.get('outsourceCenter'), data['sampleType'],
            data['testCategory'], data.get('departmentName'), data['accreditation'],
            data['integrationCode'], data['shortText'], data['capTest'],
            data['targetTAT'], data['verificationStatus'], data['targetTATHHMM'],
            data['active'], sr_no
        ))
        conn.commit()
        cursor.close()
        conn.close()
        return jsonify({'success': True, 'message': 'Test updated successfully'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@staff_bp.route('/api/tests/delete/<int:sr_no>', methods=['DELETE'])
@staff_login_required
def delete_test(sr_no):
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': 'Database connection failed'}), 500
        cursor = conn.cursor()
        cursor.execute("DELETE FROM tests WHERE SrNo = %s", (sr_no,))
        conn.commit()
        cursor.close()
        conn.close()
        return jsonify({'success': True, 'message': 'Test deleted successfully'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@staff_bp.route('/api/reports')
@staff_login_required
def get_reports():
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': 'Database connection failed'}), 500

        with conn.cursor() as cursor:
            cursor.execute("""
                SELECT 
                    r.id,
                    b.id as booking_id,
                    b.barcode,
                    COALESCE(CONCAT(up.first_name, ' ', up.last_name), u.username) as patient_name,
                    t.TestName as test_name,
                    COALESCE(sc.collection_status, 'Pending') as sample_status,
                    r.file_path,
                    r.report_url,
                    COALESCE(r.assign_status, 'Not Assigned') as assign_status,
                    COALESCE(r.report_status, 'Pending') as report_status,
                    r.report_type,
                    r.report_format,
                    r.doctor_id,
                    d.name as doctor_name,
                    r.created_at,
                    CASE WHEN pr.id IS NOT NULL THEN 1 ELSE 0 END as is_sent
                FROM reports r
                JOIN bookings b ON r.booking_id = b.id
                JOIN users u ON b.user_id = u.id
                LEFT JOIN user_profiles up ON u.id = up.user_id
                JOIN testdetails t ON b.test_id = t.SrNo
                LEFT JOIN sample_collections sc ON b.id = sc.booking_id
                LEFT JOIN doctors d ON r.doctor_id = d.id
                LEFT JOIN patient_report pr ON r.id = pr.report_id
                ORDER BY r.created_at DESC
            """)
            columns = [desc[0] for desc in cursor.description]
            reports = []
            for row in cursor.fetchall():
                report = dict(zip(columns, row))
                if report['created_at']:
                    report['created_at'] = report['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                reports.append(report)
            
            return jsonify(reports)
    except Exception as e:
        logger.error(f"Error in get_reports: {e}")
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            conn.close()

@staff_bp.route('/api/receptionist/upload-report', methods=['POST'])
@staff_login_required
def receptionist_upload_report():
    try:
        if 'report_file' not in request.files:
            return jsonify({'error': 'No file part'}), 400
        
        file = request.files['report_file']
        if file.filename == '':
            return jsonify({'error': 'No selected file'}), 400
        
        # Use secure file upload validation
        success, message, file_info = secure_file_handler.secure_upload(file, 'reports')

        if not success:
            return jsonify({'error': message}), 400
        
        booking_id = request.form.get('booking_id')
        if not booking_id:
            return jsonify({'error': 'Booking ID is required'}), 400

        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # Get booking details including patient_id and test_id
        cursor.execute("""
            SELECT b.id, b.user_id as patient_id, b.test_id, u.email, 
                   CONCAT(up.first_name, ' ', up.last_name) as patient_name,
                   t.TestName
            FROM bookings b
            JOIN users u ON b.user_id = u.id
            JOIN user_profiles up ON u.id = up.user_id
            JOIN testdetails t ON b.test_id = t.SrNo
            WHERE b.id = %s
        """, (booking_id,))
        booking = cursor.fetchone()
        
        if not booking:
            return jsonify({'error': 'Booking not found'}), 404

        # Check if report already exists
        cursor.execute("SELECT id FROM reports WHERE booking_id = %s", (booking_id,))
        if cursor.fetchone():
            return jsonify({'error': 'Report already exists for this booking'}), 400

        # File already uploaded securely, use the file info
        filename = file_info['secure_filename']

        # Create report record
        cursor.execute("""
            INSERT INTO reports (
                booking_id, patient_id, test_id, report_url, 
                report_status, created_at
            ) VALUES (%s, %s, %s, %s, %s, NOW())
        """, (
            booking_id,
            booking['patient_id'],
            booking['test_id'],
            file_info['relative_path'],  # Store relative path
            'Pending'
        ))
        report_id = cursor.lastrowid

        # Add to report history
        cursor.execute("""
            INSERT INTO report_history (
                report_id, status, action, changed_by, 
                changed_at, comments
            ) VALUES (%s, %s, %s, %s, NOW(), %s)
        """, (
            report_id,
            'Pending',
            'Created',
            session.get('user_id'),
            'Report uploaded by receptionist'
        ))

        conn.commit()
        return jsonify({
            'success': True,
            'message': 'Report uploaded successfully',
            'report_id': report_id,
            'patient_name': booking['patient_name'],
            'test_name': booking['TestName']
        })

    except Exception as e:
        logger.error(f"Error in receptionist_upload_report: {str(e)}")
        return jsonify({'error': str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

@staff_bp.route('/api/reports/assign', methods=['POST'])
@staff_login_required
def assign_report():
    try:
        data = request.get_json()
        report_id = data.get('report_id')
        doctor_id = data.get('doctor_id')
        
        if not all([report_id, doctor_id]):
            return jsonify({'error': 'Missing required fields'}), 400

        conn = get_db_connection()
        if not conn:
            return jsonify({'error': 'Database connection failed'}), 500

        with conn.cursor(dictionary=True) as cursor:
            # Get report and doctor details
            cursor.execute("""
                SELECT r.id, r.booking_id, t.TestName, 
                       CONCAT(COALESCE(up.first_name, ' '), ' ', COALESCE(up.last_name, '')) as patient_name,
                       d.name as doctor_name, d.email as doctor_email
                FROM reports r
                JOIN bookings b ON r.booking_id = b.id
                JOIN testdetails t ON b.test_id = t.SrNo
                JOIN users u ON b.user_id = u.id
                LEFT JOIN user_profiles up ON u.id = up.user_id
                JOIN doctors d ON d.id = %s
                WHERE r.id = %s
            """, (doctor_id, report_id))
            
            report_details = cursor.fetchone()
            
            if not report_details:
                return jsonify({'error': 'Report or doctor not found'}), 404

            if not report_details['doctor_email']:
                return jsonify({'error': 'Doctor email not found'}), 400

            # Update report assignment
            cursor.execute("""
                UPDATE reports 
                SET report_status = 'pending',
                    doctor_id = %s,
                    assign_status = 'Assigned',
                    updated_at = NOW()
                WHERE id = %s
            """, (doctor_id, report_id))

            # Send email notification to doctor
            subject = f"New Report Assignment - {report_details['TestName']}"
            body = f"""Dear Dr. {report_details['doctor_name']},

A new report has been assigned to you for review.

Report Details:
- Patient Name: {report_details['patient_name']}
- Test Name: {report_details['TestName']}
- Report ID: {report_id}
- Booking ID: {report_details['booking_id']}

Please login to the system to review the report.

Best regards,
CV BIOLABS Team"""

            msg = MIMEMultipart()
            msg['From'] = os.environ.get('MAIL_DEFAULT_SENDER', '<EMAIL>')
            msg['To'] = report_details['doctor_email']
            msg['Subject'] = subject
            msg.attach(MIMEText(body, 'plain'))

            # Send email using SMTP
            smtp_server = os.environ.get('MAIL_SERVER', 'smtpout.secureserver.net')
            smtp_port = int(os.environ.get('MAIL_PORT', 465))
            smtp_user = os.environ.get('MAIL_USERNAME')
            smtp_pass = os.environ.get('MAIL_PASSWORD')

            try:
                # Use SMTP_SSL for port 465 (GoDaddy requires SSL)
                server = smtplib.SMTP_SSL(smtp_server, smtp_port)
                server.login(smtp_user, smtp_pass)
                server.sendmail(msg['From'], [msg['To']], msg.as_string())
                server.quit()
            except Exception as e:
                logger.error(f"Failed to send email to doctor: {str(e)}")
                # Continue with the assignment even if email fails
                pass

            conn.commit()

            return jsonify({
                'success': True,
                'message': 'Report assigned successfully and notification sent to doctor'
            })

    except Exception as e:
        logger.error(f"Error in assign_report: {str(e)}")
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            conn.close()

@staff_bp.route('/logout')
def logout():
    # Clear all session data
    session.clear()
    
    # Logout from Flask-Login if used
    logout_user()
    
    # Clear any flash messages
    flash('You have been logged out', 'success')
    
    return redirect(url_for('home'))

@staff_bp.route('/api/patients/<int:patient_id>')
@staff_login_required
def get_patient(patient_id):
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': 'Database connection failed'}), 500

        with conn.cursor() as cursor:
            # Get patient details with user_profiles
            cursor.execute("""
                SELECT 
                    u.id,
                    u.username,
                    u.email,
                    u.status,
                    up.first_name,
                    up.last_name,
                    up.phone,
                    DATE_FORMAT(up.date_of_birth, '%Y-%m-%d') as dob,
                    up.gender,
                    (SELECT MAX(booking_date) FROM bookings WHERE user_id = u.id) as last_visit
                FROM users u
                LEFT JOIN user_profiles up ON u.id = up.user_id
                WHERE u.id = %s
            """, (patient_id,))
            patient = cursor.fetchone()
            
            if not patient:
                return jsonify({'error': 'Patient not found'}), 404
            
            # Get booking history
            cursor.execute("""
                SELECT 
                    b.id as booking_id,
                    t.TestName as test_name,
                    DATE_FORMAT(b.booking_date, '%d/%%m/%%Y') as booking_date,
                    r.report_status,
                    b.booking_status,
                    b.barcode
                FROM bookings b
                JOIN testdetails t ON b.test_id = t.SrNo
                LEFT JOIN reports r ON b.id = r.booking_id
                WHERE b.user_id = %s
                ORDER BY b.booking_date DESC
            """, (patient_id,))
            patient['booking_history'] = cursor.fetchall()
            
            # Get payment history
            cursor.execute("""
                SELECT 
                    p.id as payment_id,
                    p.amount,
                    p.payment_method,
                    DATE_FORMAT(p.payment_date, '%d/%%m/%%Y') as payment_date,
                    p.payment_status,
                    p.transaction_id
                FROM payments p
                JOIN bookings b ON p.booking_id = b.id
                WHERE b.user_id = %s
                ORDER BY p.payment_date DESC
            """, (patient_id,))
            patient['payment_history'] = cursor.fetchall()
            
            return jsonify(patient)
    except Exception as e:
        logger.error(f"Error in get_patient: {e}")
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            conn.close()

# Receptionist Dashboard API Routes
@staff_bp.route('/api/receptionist/today-bookings')
@staff_login_required
def get_today_bookings():
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': 'Database connection failed'}), 500

        with conn.cursor() as cursor:
            cursor.execute("""
                SELECT 
                    b.id,
                    u.username,
                    u.email,
                    u.phone,
                    t.TestName,
                    t.TestAmount,
                    DATE_FORMAT(b.booking_date, '%Y-%m-%d') as booking_date,
                    b.appointment_time,
                    b.booking_status,
                    b.barcode
                FROM bookings b
                JOIN users u ON b.user_id = u.id
                JOIN testdetails t ON b.test_id = t.SrNo
                WHERE DATE(b.booking_date) = CURDATE()
                ORDER BY b.appointment_time ASC
            """)
            bookings = cursor.fetchall()
            return jsonify(bookings)
    except Exception as e:
        logger.error(f"Error in get_today_bookings: {e}")
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            conn.close()

@staff_bp.route('/api/receptionist/available-agents')
@staff_login_required
def get_available_agents():
    try:
        conn = get_db_connection()
        if not conn:
            logger.error("Database connection failed")
            return jsonify({'error': 'Database connection failed'}), 500

        with conn.cursor(dictionary=True) as cursor:
            # Get available agents with their current load
            query = """
                SELECT 
                    pa.id,
                    pa.name,
                    pa.phone,
                    pa.status,
                    COALESCE(COUNT(sc.id), 0) as current_load
                FROM pickup_agents pa
                LEFT JOIN sample_collections sc ON pa.id = sc.agent_id 
                    AND DATE(sc.collection_date) = CURDATE()
                    AND sc.collection_status IN ('Pending', 'In Progress')
                WHERE pa.status = 'available'
                GROUP BY pa.id, pa.name, pa.phone, pa.status
                ORDER BY current_load ASC, pa.name ASC
            """
            cursor.execute(query)
            agents = cursor.fetchall()
            
            # Convert any Decimal values to float for JSON serialization
            for agent in agents:
                if 'current_load' in agent and agent['current_load'] is not None:
                    agent['current_load'] = int(agent['current_load'])
            
            return jsonify(agents)
    except Exception as e:
        logger.error(f"Error in get_available_agents: {str(e)}")
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            conn.close()

@staff_bp.route('/api/receptionist/assign-agent', methods=['POST'])
@staff_login_required
def assign_pickup_agent():
    try:
        data = request.get_json()
        booking_id = data.get('booking_id')
        agent_id = data.get('agent_id')
        collection_date = data.get('collection_date')

        if not all([booking_id, agent_id, collection_date]):
            return jsonify({'error': 'Missing required fields'}), 400

        conn = get_db_connection()
        if not conn:
            return jsonify({'error': 'Database connection failed'}), 500

        with conn.cursor() as cursor:
            # Check if booking exists and is valid
            cursor.execute("SELECT id FROM bookings WHERE id = %s", (booking_id,))
            if not cursor.fetchone():
                return jsonify({'error': 'Invalid booking ID'}), 400

            # Check if agent is available
            cursor.execute("""
                SELECT id FROM pickup_agents 
                WHERE id = %s AND status = 'Available'
            """, (agent_id,))
            if not cursor.fetchone():
                return jsonify({'error': 'Agent not available'}), 400

            # Create sample collection entry
            cursor.execute("""
                INSERT INTO sample_collections 
                (booking_id, agent_id, collection_date, collection_status)
                VALUES (%s, %s, %s, 'Pending')
            """, (booking_id, agent_id, collection_date))
            
            conn.commit()
            return jsonify({'success': True, 'message': 'Agent assigned successfully'})
    except Exception as e:
        logger.error(f"Error in assign_pickup_agent: {e}")
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            conn.close()

@staff_bp.route('/api/receptionist/collection-tracking')
@staff_login_required
def get_collection_tracking():
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': 'Database connection failed'}), 500

        with conn.cursor() as cursor:
            cursor.execute("""
                SELECT 
                    sc.id,
                    u.username,
                    b.barcode,
                    sc.collection_status,
                    DATE_FORMAT(sc.collection_date, '%Y-%m-%d') as collection_date,
                    pa.name as agent_name,
                    pa.phone as agent_phone
                FROM sample_collections sc
                JOIN bookings b ON sc.booking_id = b.id
                JOIN users u ON b.user_id = u.id
                LEFT JOIN pickup_agents pa ON sc.agent_id = pa.id
                WHERE DATE(sc.collection_date) = CURDATE()
                ORDER BY sc.collection_date ASC
            """)
            collections = cursor.fetchall()
            return jsonify(collections)
    except Exception as e:
        logger.error(f"Error in get_collection_tracking: {e}")
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            conn.close()

@staff_bp.route('/api/receptionist/agent-load')
@staff_login_required
def get_agent_load():
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': 'Database connection failed'}), 500

        with conn.cursor() as cursor:
            cursor.execute("""
                SELECT 
                    pa.id,
                    pa.name,
                    COUNT(sc.id) AS total_assigned,
                    SUM(CASE WHEN sc.collection_status = 'Collected' THEN 1 ELSE 0 END) AS collected_count,
                    SUM(CASE WHEN sc.collection_status = 'Pending' THEN 1 ELSE 0 END) AS pending_count
                FROM pickup_agents pa
                LEFT JOIN sample_collections sc ON sc.agent_id = pa.id 
                    AND DATE(sc.collection_date) = CURDATE()
                GROUP BY pa.id
                ORDER BY total_assigned DESC
            """)
            agent_load = cursor.fetchall()
            return jsonify(agent_load)
    except Exception as e:
        logger.error(f"Error in get_agent_load: {e}")
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            conn.close()

@staff_bp.route('/api/receptionist/search-bookings')
@staff_login_required
def search_bookings():
    try:
        username = request.args.get('username', '')
        booking_status = request.args.get('booking_status', '')
        collection_status = request.args.get('collection_status', '')
        date = request.args.get('date', '')
        agent_id = request.args.get('agent_id', '')

        conn = get_db_connection()
        if not conn:
            return jsonify({'error': 'Database connection failed'}), 500

        with conn.cursor() as cursor:
            query = """
                SELECT 
                    b.id,
                    u.username,
                    u.email,
                    u.phone,
                    t.TestName,
                    t.TestAmount,
                    DATE_FORMAT(b.booking_date, '%Y-%m-%d') as booking_date,
                    b.appointment_time,
                    b.booking_status,
                    b.barcode,
                    sc.collection_status,
                    pa.name as agent_name
                FROM bookings b
                JOIN users u ON b.user_id = u.id
                JOIN testdetails t ON b.test_id = t.SrNo
                LEFT JOIN sample_collections sc ON b.id = sc.booking_id
                LEFT JOIN pickup_agents pa ON sc.agent_id = pa.id
                WHERE 1=1
            """
            params = []

            if username:
                query += " AND u.username LIKE %s"
                params.append(f"%{username}%")
            if booking_status:
                query += " AND b.booking_status = %s"
                params.append(booking_status)
            if collection_status:
                query += " AND sc.collection_status = %s"
                params.append(collection_status)
            if date:
                query += " AND DATE(b.booking_date) = %s"
                params.append(date)
            if agent_id:
                query += " AND sc.agent_id = %s"
                params.append(agent_id)

            query += " ORDER BY b.booking_date DESC, b.appointment_time ASC"
            
            cursor.execute(query, params)
            bookings = cursor.fetchall()
            return jsonify(bookings)
    except Exception as e:
        logger.error(f"Error in search_bookings: {e}")
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            conn.close()

@staff_bp.route('/api/receptionist/export-data')
@staff_login_required
def export_data():
    try:
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        export_type = request.args.get('type', 'bookings')  # 'bookings' or 'collections'

        if not all([start_date, end_date]):
            return jsonify({'error': 'Start date and end date are required'}), 400

        conn = get_db_connection()
        if not conn:
            return jsonify({'error': 'Database connection failed'}), 500

        with conn.cursor() as cursor:
            if export_type == 'bookings':
                cursor.execute("""
                    SELECT 
                        b.id,
                        u.username,
                        u.email,
                        u.phone,
                        t.TestName,
                        t.TestAmount,
                        DATE_FORMAT(b.booking_date, '%Y-%m-%d') as booking_date,
                        b.appointment_time,
                        b.booking_status,
                        b.barcode
                    FROM bookings b
                    JOIN users u ON b.user_id = u.id
                    JOIN testdetails t ON b.test_id = t.SrNo
                    WHERE DATE(b.booking_date) BETWEEN %s AND %s
                    ORDER BY b.booking_date, b.appointment_time
                """, (start_date, end_date))
            else:  # collections
                cursor.execute("""
                    SELECT 
                        sc.id,
                        u.username,
                        b.barcode,
                        sc.collection_status,
                        DATE_FORMAT(sc.collection_date, '%Y-%m-%d') as collection_date,
                        pa.name as agent_name,
                        pa.phone as agent_phone
                    FROM sample_collections sc
                    JOIN bookings b ON sc.booking_id = b.id
                    JOIN users u ON b.user_id = u.id
                    LEFT JOIN pickup_agents pa ON sc.agent_id = pa.id
                    WHERE DATE(sc.collection_date) BETWEEN %s AND %s
                    ORDER BY sc.collection_date
                """, (start_date, end_date))

            data = cursor.fetchall()

            # Create Excel file
            wb = Workbook()
            ws = wb.active
            
            # Add headers
            headers = list(data[0].keys()) if data else []
            ws.append(headers)
            
            # Add data
            for row in data:
                ws.append(list(row.values()))
            
            # Save to BytesIO
            excel_file = io.BytesIO()
            wb.save(excel_file)
            excel_file.seek(0)
            
            return send_file(
                excel_file,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                as_attachment=True,
                download_name=f'{export_type}_report_{start_date}_to_{end_date}.xlsx'
            )
    except Exception as e:
        logger.error(f"Error in export_data: {e}")
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            conn.close()

@staff_bp.route('/api/receptionist/send-reminder', methods=['POST'])
@staff_login_required
def send_reminder():
    try:
        data = request.get_json()
        reminder_type = data.get('type')  # 'patient' or 'agent'
        recipient_id = data.get('recipient_id')
        message = data.get('message')

        if not all([reminder_type, recipient_id, message]):
            return jsonify({'error': 'Missing required fields'}), 400

        conn = get_db_connection()
        if not conn:
            return jsonify({'error': 'Database connection failed'}), 500

        with conn.cursor() as cursor:
            if reminder_type == 'patient':
                cursor.execute("""
                    SELECT phone FROM users WHERE id = %s
                """, (recipient_id,))
            else:  # agent
                cursor.execute("""
                    SELECT phone FROM pickup_agents WHERE id = %s
                """, (recipient_id,))

            result = cursor.fetchone()
            if not result:
                return jsonify({'error': 'Recipient not found'}), 404

            phone = result['phone']
            
            # TODO: Integrate with WhatsApp/SMS API
            # For now, just log the reminder
            logger.debug(f"Sending reminder to recipient ID {recipient_id}")
            
            return jsonify({
                'success': True,
                'message': 'Reminder queued for sending',
                'recipient': phone
            })
    except Exception as e:
        logger.error(f"Error sending reminder: {str(e)}")
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            conn.close()

@staff_bp.route('/api/receptionist/dashboard')
@staff_login_required
def receptionist_dashboard():
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({"error": "Database connection failed"}), 500
            
        with conn.cursor(dictionary=True) as cursor:
            # Get total appointments for today
            cursor.execute("""
                SELECT COALESCE(COUNT(*), 0) as count 
                FROM bookings 
                WHERE DATE(booking_date) = CURDATE()
            """)
            result = cursor.fetchone()
            total_appointments = result['count'] if result else 0
            
            # Get pending collections
            cursor.execute("""
                SELECT COALESCE(COUNT(*), 0) as count 
                FROM sample_collections 
                WHERE collection_status = 'Pending'
            """)
            result = cursor.fetchone()
            pending_collections = result['count'] if result else 0
            
            # Get total revenue for today
            cursor.execute("""
                SELECT COALESCE(SUM(p.amount), 0) as total 
                FROM payments p 
                JOIN bookings b ON p.booking_id = b.id 
                WHERE DATE(b.booking_date) = CURDATE() 
                AND p.payment_status = 'paid'
            """)
            today_revenue = float(cursor.fetchone()['total'])
            
            # Get recent bookings
            cursor.execute("""
                SELECT 
                    b.id,
                    b.booking_date,
                    TIME_FORMAT(b.appointment_time, '%H:%i') as appointment_time,
                    t.TestName,
                    CONCAT(COALESCE(up.first_name, ''), ' ', COALESCE(up.last_name, '')) as patient_name,
                    b.booking_status,
                    b.barcode
                FROM bookings b
                JOIN testdetails t ON b.test_id = t.SrNo
                JOIN user_profiles up ON b.user_id = up.user_id
                WHERE DATE(b.booking_date) = CURDATE()
                ORDER BY b.booking_date DESC
                LIMIT 5
            """)
            recent_bookings = cursor.fetchall()
            
            # Format dates in recent bookings
            for booking in recent_bookings:
                if booking['booking_date']:
                    booking['booking_date'] = booking['booking_date'].strftime('%Y-%m-%d')
            
            # Get available agents
            cursor.execute("""
                SELECT COALESCE(COUNT(*), 0) as count 
                FROM pickup_agents 
                WHERE status = 'Available'
            """)
            result = cursor.fetchone()
            available_agents = result['count'] if result else 0
            
            return jsonify({
                "total_appointments": total_appointments,
                "scheduled_pickups": pending_collections,
                "pending_reports": 0,  # Initialize with 0 since this endpoint doesn't track reports
                "today_revenue": today_revenue,
                "recent_bookings": recent_bookings,
                "available_agents": available_agents
            })
            
    except Exception as e:
        logger.error(f"Dashboard error: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        if 'conn' in locals():
            conn.close()

@staff_bp.route('/api/receptionist/appointments')
@staff_login_required
def receptionist_appointments():
    try:
        conn = get_db_connection()
        if not conn:
            logger.error("Database connection failed")
            return jsonify({'error': 'Database connection failed'}), 500

        # Get query parameters
        search = request.args.get('search', '')
        status = request.args.get('status', '')
        date = request.args.get('date', '')

        with conn.cursor(dictionary=True) as cursor:
            # Build the base query
            query = """
                SELECT 
                    b.id,
                    b.barcode,
                    b.booking_status,
                    DATE_FORMAT(b.booking_date, '%Y-%m-%d') as booking_date,
                    TIME_FORMAT(b.appointment_time, '%H:%i') as appointment_time,
                    CONCAT(COALESCE(up.first_name, ''), ' ', COALESCE(up.last_name, '')) as patient_name,
                    t.TestName as test_name,
                    t.TestAmount as test_amount,
                    COALESCE(p.payment_status, 'Pending') as payment_status,
                    COALESCE(p.amount, 0) as payment_amount,
                    COALESCE(sc.collection_status, 'Pending') as collection_status,
                    COALESCE(pa.name, '') as agent_name,
                    pa.id as agent_id
                FROM bookings b
                LEFT JOIN users u ON b.user_id = u.id
                LEFT JOIN user_profiles up ON u.id = up.user_id
                LEFT JOIN testdetails t ON b.test_id = t.SrNo
                LEFT JOIN payments p ON b.id = p.booking_id
                LEFT JOIN sample_collections sc ON b.id = sc.booking_id
                LEFT JOIN pickup_agents pa ON sc.agent_id = pa.id
                WHERE b.booking_status != 'cancelled'
            """
            params = []

            # Add search condition
            if search:
                query += """ AND (
                    CAST(b.id AS CHAR) LIKE %s
                    OR b.barcode LIKE %s
                    OR CONCAT(COALESCE(up.first_name, ''), ' ', COALESCE(up.last_name, '')) LIKE %s
                    OR t.TestName LIKE %s
                )"""
                search_param = f"%{search}%"
                params.extend([search_param, search_param, search_param, search_param])

            # Add status filter
            if status:
                if status == 'assigned':
                    query += " AND pa.id IS NOT NULL"
                elif status == 'unassigned':
                    query += " AND pa.id IS NULL"

            # Add date filter
            if date:
                query += " AND DATE(b.booking_date) = %s"
                params.append(date)

            # Add ordering
            query += " ORDER BY b.booking_date DESC, b.appointment_time ASC"
            
            logger.debug("Executing appointments query")
            cursor.execute(query, params)
            appointments = cursor.fetchall()
            logger.debug(f"Retrieved {len(appointments)} appointments")
            
            # Convert Decimal to float for JSON serialization
            for appointment in appointments:
                if 'test_amount' in appointment and appointment['test_amount'] is not None:
                    appointment['test_amount'] = float(appointment['test_amount'])
                if 'payment_amount' in appointment and appointment['payment_amount'] is not None:
                    appointment['payment_amount'] = float(appointment['payment_amount'])
            
            return jsonify(appointments)
    except Exception as e:
        logger.error(f"Error retrieving appointments: {str(e)}")
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            conn.close()

@staff_bp.route('/api/receptionist/sample-collections')
@staff_login_required
def receptionist_sample_collections():
    try:
        conn = get_db_connection()
        if not conn:
            logger.error("Database connection failed")
            return jsonify({'error': 'Database connection failed'}), 500

        # Get query parameters
        search = request.args.get('search', '')
        status = request.args.get('status', '')
        agent_id = request.args.get('agent_id', '')
        date = request.args.get('date', '')

        with conn.cursor(dictionary=True) as cursor:
            # Build the base query
            query = """
                SELECT 
                sc.id,
                CONCAT(COALESCE(up.first_name, ''), ' ', COALESCE(up.last_name, '')) as patient_name,
                t.TestName as test_name,
                DATE_FORMAT(sc.collection_date, '%Y-%m-%d') as collection_date,
                sc.collection_status,
                pa.name as agent_name,
                pa.id as agent_id,
                b.barcode,
                DATE_FORMAT(b.booking_date, '%Y-%m-%d') as booking_date,
                TIME_FORMAT(b.appointment_time, '%H:%i') as appointment_time
            FROM sample_collections sc
                LEFT JOIN bookings b ON sc.booking_id = b.id
                LEFT JOIN users u ON b.user_id = u.id
                LEFT JOIN user_profiles up ON u.id = up.user_id
                LEFT JOIN testdetails t ON b.test_id = t.SrNo
                LEFT JOIN pickup_agents pa ON sc.agent_id = pa.id
                WHERE 1=1
            """
            params = []

            # Add search condition
            if search:
                query += """ AND (
                    CONCAT(COALESCE(up.first_name, ''), ' ', COALESCE(up.last_name, '')) LIKE %s
                    OR t.TestName LIKE %s
                    OR b.barcode LIKE %s
                )"""
                search_param = f"%{search}%"
                params.extend([search_param, search_param, search_param])

            # Add status filter
            if status:
                if status == 'assigned':
                    query += " AND pa.id IS NOT NULL"
                elif status == 'unassigned':
                    query += " AND pa.id IS NULL"

            # Add agent filter
            if agent_id:
                query += " AND pa.id = %s"
                params.append(agent_id)

            # Add date filter
            if date:
                query += " AND DATE(sc.collection_date) = %s"
                params.append(date)

            # Add ordering
            query += " ORDER BY sc.collection_date DESC"
            
            cursor.execute(query, params)
            collections = cursor.fetchall()
            
            return jsonify(collections)
    except Exception as e:
        logger.error(f"Error in receptionist_sample_collections: {e}")
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            conn.close()

@staff_bp.route('/api/receptionist/pickup-agents')
@staff_login_required
def get_pickup_agents():
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # Get agents with their current load (count of pending collections)
        cursor.execute("""
            SELECT 
                pa.id,
                pa.name,
                pa.phone,
                pa.email,
                pa.status,
                pa.professional_id,
                pa.vehicle_number,
                pa.service_area,
                COUNT(sc.id) as current_load
            FROM pickup_agents pa
            LEFT JOIN sample_collections sc ON sc.agent_id = pa.id 
                AND sc.collection_status = 'Pending'
            GROUP BY pa.id
            ORDER BY pa.name
        """)
        agents = cursor.fetchall()
        
        cursor.close()
        conn.close()
        
        return jsonify(agents)
        
    except Exception as e:
        logger.error(f"Dashboard error: {str(e)}")
        return jsonify({'error': 'Failed to fetch pickup agents'}), 500

@staff_bp.route('/api/receptionist/agent/<int:agent_id>')
@staff_login_required
def get_agent_details(agent_id):
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # Get agent details
        cursor.execute("""
            SELECT 
                id, 
                name, 
                phone, 
                email, 
                status,
                professional_id,
                vehicle_number,
                service_area,
                created_at
            FROM pickup_agents 
            WHERE id = %s
        """, (agent_id,))
        agent = cursor.fetchone()
        
        if not agent:
            return jsonify({'error': 'Agent not found'}), 404
            
        # Get total collections count
        cursor.execute("""
            SELECT COUNT(*) as total_collections
            FROM sample_collections
            WHERE agent_id = %s
        """, (agent_id,))
        collections = cursor.fetchone()
        agent['total_collections'] = collections['total_collections'] if collections else 0
        
        # Get current load (pending/in progress collections)
        cursor.execute("""
            SELECT COUNT(*) as current_load
            FROM sample_collections
            WHERE agent_id = %s AND collection_status IN ('Pending', 'In Progress')
        """, (agent_id,))
        current = cursor.fetchone()
        agent['current_load'] = current['current_load'] if current else 0
                
        # Get success rate (collections marked as delivered)
        cursor.execute("""
            SELECT COUNT(*) as completed_collections
            FROM sample_collections
            WHERE agent_id = %s AND collection_status = 'Delivered'
        """, (agent_id,))
        completed = cursor.fetchone()
        total = agent['total_collections']
        agent['success_rate'] = round((completed['completed_collections'] / total * 100) if total > 0 else 0, 2)
        
        # Get today's schedule
        today = datetime.now().strftime('%Y-%m-%d')
        cursor.execute("""
            SELECT 
                sc.collection_date as time,
                b.barcode,
                u.username as patient_name,
                sc.collection_status as status
            FROM sample_collections sc
            JOIN bookings b ON sc.booking_id = b.id
            JOIN users u ON b.user_id = u.id
            WHERE sc.agent_id = %s 
            AND DATE(sc.collection_date) = %s
            ORDER BY sc.collection_date
        """, (agent_id, today))
        schedule = cursor.fetchall()
        agent['today_schedule'] = schedule
        
        cursor.close()
        conn.close()
        
        return jsonify(agent)
        
    except Exception as e:
        logger.error(f"Dashboard error: {str(e)}")
        return jsonify({'error': 'Failed to fetch agent details'}), 500

@staff_bp.route('/api/receptionist/update-agent-status', methods=['POST'])
@staff_login_required
def update_agent_status():
    try:
        data = request.get_json()
        agent_id = data.get('agent_id')
        status = data.get('status')
        notes = data.get('notes')
        
        if not agent_id or not status:
            return jsonify({'error': 'Missing required fields'}), 400
            
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Update agent status
        cursor.execute("""
            UPDATE pickup_agents 
            SET status = %s
            WHERE id = %s
        """, (status, agent_id))
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return jsonify({'message': 'Agent status updated successfully'})
        
    except Exception as e:
        logger.error(f"Dashboard error: {str(e)}")
        return jsonify({'error': 'Failed to update agent status'}), 500

@staff_bp.route('/api/receptionist/booking/<int:booking_id>')
@staff_login_required
def get_booking_details(booking_id):
    try:
        conn = get_db_connection()
        if not conn:
            logger.error("Database connection failed")
            return jsonify({'error': 'Database connection failed'}), 500

        with conn.cursor(dictionary=True) as cursor:
            query = """
                SELECT 
                    b.id,
                    b.barcode,
                    b.booking_status,
                    DATE_FORMAT(b.booking_date, '%Y-%m-%d') as booking_date,
                    TIME_FORMAT(b.appointment_time, '%H:%i') as appointment_time,
                    CONCAT(COALESCE(up.first_name, ''), ' ', COALESCE(up.last_name, '')) as patient_name,
                    t.TestName as test_name,
                    t.TestAmount as test_amount,
                    COALESCE(p.payment_status, 'Pending') as payment_status,
                    COALESCE(p.amount, 0) as payment_amount,
                    COALESCE(sc.collection_status, 'Pending') as collection_status,
                    COALESCE(pa.name, '') as agent_name,
                    pa.id as agent_id,
                    b.address_line1, b.address_line2, b.city, b.state, b.postal_code, b.country
                FROM bookings b
                LEFT JOIN users u ON b.user_id = u.id
                LEFT JOIN user_profiles up ON u.id = up.user_id
                LEFT JOIN testdetails t ON b.test_id = t.SrNo
                LEFT JOIN payments p ON b.id = p.booking_id
                LEFT JOIN sample_collections sc ON b.id = sc.booking_id
                LEFT JOIN pickup_agents pa ON sc.agent_id = pa.id
                WHERE b.id = %s
            """
            cursor.execute(query, (booking_id,))
            booking = cursor.fetchone()
            
            if not booking:
                return jsonify({'error': 'Booking not found'}), 404

            # Combine address fields
            address_parts = [
                booking.get('address_line1', ''),
                booking.get('address_line2', ''),
                booking.get('city', ''),
                booking.get('state', ''),
                booking.get('postal_code', ''),
                booking.get('country', '')
            ]
            booking['address'] = ', '.join([part for part in address_parts if part])

            # Convert Decimal to float for JSON serialization
            if booking['test_amount'] is not None:
                booking['test_amount'] = float(booking['test_amount'])
            if booking['payment_amount'] is not None:
                booking['payment_amount'] = float(booking['payment_amount'])

            return jsonify(booking)
    except Exception as e:
        logger.error(f"Error in get_booking_details: {str(e)}")
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            conn.close()

@staff_bp.route('/api/reports/available-bookings')
@staff_login_required
def available_bookings():
    try:
        search = request.args.get('search', '')
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': 'Database connection failed'}), 500
        with conn.cursor(dictionary=True) as cursor:
            # Get bookings that do not have a completed report
            query = '''
                SELECT b.id, b.booking_date, b.user_id, t.TestName, 
                       COALESCE(CONCAT(up.first_name, ' ', up.last_name, ''), u.username) as patient_name
                FROM bookings b
                JOIN testdetails t ON b.test_id = t.SrNo
                JOIN users u ON b.user_id = u.id
                LEFT JOIN user_profiles up ON u.id = up.user_id
                LEFT JOIN reports r ON b.id = r.booking_id AND r.report_status = 'completed'
                WHERE r.id IS NULL
            '''
            params = []
            
            if search:
                query += ''' AND (
                    CAST(b.id AS CHAR) LIKE %s
                    OR COALESCE(CONCAT(up.first_name, ' ', up.last_name, ''), u.username) LIKE %s
                    OR t.TestName LIKE %s
                    OR DATE_FORMAT(b.booking_date, '%Y-%m-%d') LIKE %s
                )'''
                search_param = f'%{search}%'
                params.extend([search_param, search_param, search_param, search_param])
            
            query += ' ORDER BY b.booking_date DESC'
            
            cursor.execute(query, params)
            bookings = cursor.fetchall()
            
            # Convert datetime objects to strings
            for booking in bookings:
                if booking['booking_date']:
                    booking['booking_date'] = booking['booking_date'].strftime('%Y-%m-%d')
            
            return jsonify(bookings)
    except Exception as e:
        logger.error(f"Error in available_bookings: {e}")
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            conn.close()

# Serve uploaded reports (PDFs) from the correct absolute path
@staff_bp.route('/uploads/reports/<path:filename>')
def serve_report_file(filename):
    try:
        # Get the absolute path to the uploads directory
        uploads_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads', 'reports')
        
        # Ensure the uploads directory exists
        if not os.path.exists(uploads_dir):
            os.makedirs(uploads_dir)
            
        # Check if file exists
        file_path = os.path.join(uploads_dir, filename)
        if not os.path.exists(file_path):
            # Try without the 'reports/' prefix
            filename_without_prefix = os.path.basename(filename)
            file_path = os.path.join(uploads_dir, filename_without_prefix)
            if not os.path.exists(file_path):
                logger.error(f"File not found: {filename} or {filename_without_prefix}")
                return jsonify({'error': 'File not found'}), 404

        # Send the file
        return send_from_directory(uploads_dir, os.path.basename(file_path), as_attachment=False)
    except Exception as e:
        logger.error(f"Error serving file {filename}: {str(e)}")
        return jsonify({'error': 'File not found'}), 404

@staff_bp.route('/api/receptionist/doctors')
@staff_login_required
def get_doctors():
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': 'Database connection failed'}), 500
        with conn.cursor(dictionary=True) as cursor:
            cursor.execute("""
                SELECT id, name, specialization, email, phone, status
                FROM doctors
                WHERE status = 'active'
                ORDER BY name
            """)
            doctors = cursor.fetchall()
        return jsonify(doctors)
    except Exception as e:
        logger.error(f"Error in get_doctors: {e}")
        return jsonify({'error': str(e)}), 500
    finally:
        if 'conn' in locals() and conn:
            conn.close()



# Note: send_report_to_patient endpoint moved to main app.py with CSRF exemption
# This avoids CSRF token conflicts while maintaining security through session authentication

@staff_bp.route('/api/reports/<int:report_id>', methods=['GET'])
@staff_login_required
def get_report_details(report_id):
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': 'Database connection failed'}), 500
        with conn.cursor(dictionary=True) as cursor:
            cursor.execute("""
                SELECT 
                    r.id as report_id,
                    r.booking_id,
                    r.report_url,
                    r.doctor_review,
                    r.report_status,
                    r.created_at,
                    b.user_id as patient_id,
                    b.barcode,
                    t.TestName,
                    u.email,
                    CONCAT(COALESCE(up.first_name, ''), ' ', COALESCE(up.last_name, '')) as patient_name,
                    d.name as doctor_name
                FROM reports r
                JOIN bookings b ON r.booking_id = b.id
                JOIN users u ON b.user_id = u.id
                LEFT JOIN user_profiles up ON u.id = up.user_id
                JOIN testdetails t ON b.test_id = t.SrNo
                LEFT JOIN doctors d ON r.doctor_id = d.id
                WHERE r.id = %s
            """, (report_id,))
            report = cursor.fetchone()
            if not report:
                return jsonify({'error': 'Report not found'}), 404
            
            # Format dates
            if report.get('created_at'):
                report['created_at'] = report['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            
            return jsonify(report)
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        if 'conn' in locals() and conn:
            conn.close()

@staff_bp.route('/api/reports/<int:report_id>/download')
@staff_login_required
def download_report(report_id):
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # Get report details
        cursor.execute("""
            SELECT r.*, b.barcode, t.TestName
            FROM reports r
            JOIN bookings b ON r.booking_id = b.id
            JOIN testdetails t ON b.test_id = t.SrNo
            WHERE r.id = %s
        """, (report_id,))
        report = cursor.fetchone()
        
        if not report:
            return jsonify({'error': 'Report not found'}), 404
            
        if not report['report_url']:
            return jsonify({'error': 'Report file not found'}), 404
            
        # Get the file path
        uploads_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
        # Handle case where report_url already contains 'reports/' prefix
        report_url = report['report_url']
        if report_url.startswith('reports/'):
            file_path = os.path.join(uploads_dir, report_url)
        else:
            file_path = os.path.join(uploads_dir, 'reports', report_url)

        if not os.path.exists(file_path):
            return jsonify({'error': 'Report file not found'}), 404
            
        # Generate a nice filename
        filename = f"{report['TestName']}_{report['barcode']}.pdf"
        
        return send_file(
            file_path,
            as_attachment=True,
            download_name=filename,
            mimetype='application/pdf'
        )
        
    except Exception as e:
        logger.error(f"Error downloading report {report_id}: {str(e)}")
        return jsonify({'error': str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

# Protect all other staff dashboard and receptionist routes
protected_routes = [
    '/api/patients', '/api/patients/add', '/api/patients/update/<int:patient_id>', '/api/patients/delete/<int:patient_id>',
    '/api/pickups', '/api/pickups/add', '/api/payments', '/api/payments/add', '/api/tests', '/api/tests/add', '/api/tests/update/<int:sr_no>', '/api/tests/delete/<int:sr_no>',
    '/api/reports', '/api/reports/assign', '/api/reports/send-to-patient', '/api/receptionist/upload-report', '/api/receptionist/today-bookings', '/api/receptionist/available-agents', '/api/receptionist/assign-agent', '/api/receptionist/collection-tracking', '/api/receptionist/agent-load', '/api/receptionist/search-bookings', '/api/receptionist/export-data', '/api/receptionist/send-reminder', '/api/receptionist/dashboard', '/api/receptionist/appointments', '/api/receptionist/sample-collections', '/api/receptionist/pickup-agents', '/api/receptionist/agent/<int:agent_id>', '/api/receptionist/update-agent-status', '/api/receptionist/booking/<int:booking_id>', '/api/reports/available-bookings'
]

for route in protected_routes:
    view_func = staff_bp.view_functions.get(f'staff.{route.split("/")[-1].replace("<int:patient_id>", "update_patient").replace("<int:sr_no>", "update_test").replace("<int:agent_id>", "get_agent_details").replace("<int:booking_id>", "get_booking_details")}')
    if view_func:
        staff_bp.view_functions[view_func.__name__] = staff_login_required(view_func)

# Add after the staff_bp creation
staff_bp.config = {
    'UPLOAD_FOLDER': os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
}