#!/usr/bin/env python3
"""
Test script to verify CSRF exemption is working for staff endpoint
"""

import requests
import json
import sys

def test_csrf_exemption():
    """Test that the staff endpoint works without CSRF token"""
    print("🧪 Testing CSRF Exemption for Staff Email Endpoint")
    print("=" * 55)
    
    base_url = "http://127.0.0.1:7000"
    endpoint = "/staff/api/reports/send-to-patient"
    
    print(f"🌐 Testing endpoint: {base_url}{endpoint}")
    print("📝 Note: This test requires the Flask app to be running")
    
    # Check if server is running
    try:
        response = requests.get(base_url, timeout=5)
        print("✅ Server is running")
    except requests.exceptions.RequestException:
        print("❌ Server is not running. Please start the Flask app first.")
        return False
    
    print("\n🔍 Testing CSRF exemption...")
    
    # Test 1: Request without CSRF token (should work now)
    print("\n1. Testing request WITHOUT CSRF token:")
    try:
        response = requests.post(
            f"{base_url}{endpoint}",
            json={'report_id': 1},
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 400:
            try:
                error_data = response.json()
                error_msg = error_data.get('error', '')
                if 'CSRF' in error_msg:
                    print(f"   ❌ CSRF error still occurring: {error_msg}")
                    return False
                else:
                    print(f"   ✅ No CSRF error (got different validation error): {error_msg}")
            except:
                print(f"   ✅ No CSRF error (non-JSON response)")
        elif response.status_code == 401:
            print(f"   ✅ Authentication error (expected without login)")
        else:
            print(f"   ✅ No CSRF error (status: {response.status_code})")
            
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Request failed: {str(e)}")
        return False
    
    # Test 2: Request with invalid CSRF token (should still work)
    print("\n2. Testing request WITH invalid CSRF token:")
    try:
        response = requests.post(
            f"{base_url}{endpoint}",
            json={'report_id': 1},
            headers={
                'Content-Type': 'application/json',
                'X-CSRFToken': 'invalid-token-12345'
            },
            timeout=10
        )
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 400:
            try:
                error_data = response.json()
                error_msg = error_data.get('error', '')
                if 'CSRF' in error_msg:
                    print(f"   ❌ CSRF error still occurring: {error_msg}")
                    return False
                else:
                    print(f"   ✅ No CSRF error (got different validation error): {error_msg}")
            except:
                print(f"   ✅ No CSRF error (non-JSON response)")
        elif response.status_code == 401:
            print(f"   ✅ Authentication error (expected without login)")
        else:
            print(f"   ✅ No CSRF error (status: {response.status_code})")
            
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Request failed: {str(e)}")
        return False
    
    print("\n" + "=" * 55)
    print("✅ CSRF exemption is working correctly!")
    print("\n📋 Summary:")
    print("   • Endpoint accepts requests without CSRF tokens")
    print("   • No CSRF validation errors occurring")
    print("   • Authentication still required (session-based)")
    print("   • Ready for staff members to use")
    
    return True

if __name__ == "__main__":
    success = test_csrf_exemption()
    print(f"\nResult: {'✅ PASS' if success else '❌ FAIL'}")
    sys.exit(0 if success else 1)
