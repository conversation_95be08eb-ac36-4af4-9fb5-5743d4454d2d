#!/usr/bin/env python3
"""
Test script to verify the database query fix for staff email endpoint
"""

import requests
import json
import sys

def test_database_fix():
    """Test that the database query is now working correctly"""
    print("🗄️  Testing Database Query Fix for Staff Email Endpoint")
    print("=" * 65)
    
    base_url = "http://127.0.0.1:7000"
    endpoint = "/staff/api/reports/send-to-patient"
    
    print(f"🌐 Testing endpoint: {base_url}{endpoint}")
    print("📝 This test verifies the database query is using correct tables")
    
    # Check if server is running
    try:
        response = requests.get(base_url, timeout=5)
        print("✅ Server is running")
    except requests.exceptions.RequestException:
        print("❌ Server is not running. Please start the Flask app first.")
        print("   Command: python app.py")
        return False
    
    print("\n🔍 Testing database query fix...")
    
    # Test with a valid report ID format
    print("\n1. Testing with valid report ID (should not get table error):")
    try:
        response = requests.post(
            f"{base_url}{endpoint}",
            json={'report_id': 1},
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"   Status Code: {response.status_code}")
        
        # Check response content
        try:
            response_data = response.json()
            error_msg = response_data.get('error', '')
            
            # Check for database table errors (these should NOT occur)
            if "Table 'cvbiolabs.patients' doesn't exist" in error_msg:
                print(f"   ❌ Database table error still occurring!")
                print(f"   Error: {error_msg}")
                return False
            elif "patients" in error_msg.lower() and "doesn't exist" in error_msg.lower():
                print(f"   ❌ Database table error still occurring!")
                print(f"   Error: {error_msg}")
                return False
            else:
                print(f"   ✅ No database table errors!")
                if response.status_code == 401:
                    print(f"   ✅ Got authentication error (expected): {error_msg}")
                elif response.status_code == 400:
                    print(f"   ✅ Got validation error (expected): {error_msg}")
                elif response.status_code == 404:
                    print(f"   ✅ Got not found error (expected): {error_msg}")
                elif response.status_code == 500:
                    print(f"   ⚠️  Got server error: {error_msg}")
                    # Check if it's a different database error
                    if "mysql" in error_msg.lower() or "database" in error_msg.lower():
                        print(f"   ℹ️  This might be a different database issue")
                    else:
                        print(f"   ✅ Not a table existence error")
                else:
                    print(f"   ✅ Got other response: {error_msg}")
                    
        except json.JSONDecodeError:
            print(f"   ✅ Non-JSON response (no database table error)")
            
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Request failed: {str(e)}")
        return False
    
    # Test with different report IDs
    test_ids = [2, 3, 999]
    for test_id in test_ids:
        print(f"\n2.{test_ids.index(test_id) + 1} Testing with report ID {test_id}:")
        try:
            response = requests.post(
                f"{base_url}{endpoint}",
                json={'report_id': test_id},
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            print(f"   Status Code: {response.status_code}")
            
            try:
                response_data = response.json()
                error_msg = response_data.get('error', '')
                
                if "Table 'cvbiolabs.patients' doesn't exist" in error_msg:
                    print(f"   ❌ Database table error still occurring!")
                    return False
                else:
                    print(f"   ✅ No table existence errors")
                    
            except json.JSONDecodeError:
                print(f"   ✅ Non-JSON response (no database error)")
                
        except requests.exceptions.RequestException as e:
            print(f"   ❌ Request failed: {str(e)}")
            return False
    
    print("\n" + "=" * 65)
    print("🎉 Database Query Fix Successful!")
    print("\n📋 Summary of Database Fix:")
    print("   • Changed FROM patients p to FROM users u")
    print("   • Updated JOIN to use users and user_profiles tables")
    print("   • Fixed patient name concatenation from user_profiles")
    print("   • Corrected field references in INSERT statement")
    print("   • Added proper testdetails JOIN for TestName")
    
    print("\n✅ The 'patients table doesn't exist' error is resolved!")
    print("📧 Staff can now send emails without database errors.")
    
    return True

if __name__ == "__main__":
    success = test_database_fix()
    print(f"\nFinal Result: {'✅ DATABASE FIXED' if success else '❌ DATABASE STILL BROKEN'}")
    sys.exit(0 if success else 1)
