#!/usr/bin/env python3
"""
Simple test to verify email service integration
"""

import os
import sys
from flask import Flask
from flask_mail import Mail

def test_email_service_simple():
    """Test email service integration in isolation"""
    print("🧪 Testing Email Service Integration")
    print("=" * 40)
    
    try:
        # Create a minimal Flask app
        app = Flask(__name__)
        app.config.update({
            'MAIL_SERVER': 'smtp.gmail.com',
            'MAIL_PORT': 587,
            'MAIL_USE_TLS': True,
            'MAIL_USERNAME': '<EMAIL>',
            'MAIL_PASSWORD': 'test',
            'MAIL_DEFAULT_SENDER': '<EMAIL>',
            'MAIL_SUPPRESS_SEND': True  # Don't actually send emails
        })
        
        with app.app_context():
            # Initialize mail
            mail = Mail(app)
            print("✅ Flask-Mail initialized")
            
            # Initialize email service
            from email_service import init_email_service, get_email_service
            email_service = init_email_service(mail)
            print("✅ Email service initialized")
            
            # Test getting the service
            service = get_email_service()
            print("✅ Email service retrieved")
            
            # Test the test report method exists
            if hasattr(service, 'send_test_report_ready'):
                print("✅ send_test_report_ready method exists")
            else:
                print("❌ send_test_report_ready method missing")
                return False
            
            print("\n✅ All email service tests passed!")
            return True
            
    except Exception as e:
        print(f"❌ Email service test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_email_service_simple()
    print(f"\nResult: {'✅ PASS' if success else '❌ FAIL'}")
    sys.exit(0 if success else 1)
