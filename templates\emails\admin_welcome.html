{% extends "emails/base.html" %}

{% block title %}Welcome to CVBioLabs - {{ user_role }} Account Created{% endblock %}

{% block content %}
<!-- Account Creation Summary -->
<div style="background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%); border-left: 4px solid #002f6c; padding: 20px; margin-bottom: 25px; border-radius: 0 10px 10px 0;">
    <div style="font-size: 16px; color: #002f6c; font-weight: 600; margin-bottom: 10px;">
        <i class="fas fa-user-plus"></i> Account Created Successfully
    </div>
    <div style="font-size: 14px; color: #333; line-height: 1.5;">
        <strong>Professional ID:</strong> {{ professional_id }}<br>
        <strong>Role:</strong> {{ user_role }}<br>
        <strong>Email:</strong> {{ user_email }}
    </div>
</div>

<div class="greeting">
    {% if user_role == 'Doctor' %}
        Dear Dr. {{ user_name }},
    {% else %}
        Dear {{ user_name }},
    {% endif %}
</div>

<div class="content-text">
    {% if user_role == 'Doctor' %}
        Welcome to <strong>CVBioLabs</strong>! We're delighted to have you join our medical team. Your doctor account has been successfully created by our administration team, and you now have access to our comprehensive medical diagnostic platform.
    {% elif user_role == 'Pickup Agent' %}
        Welcome to <strong>CVBioLabs</strong>! Your pickup agent account has been successfully created by our administration team. You're now part of our dedicated field operations team.
    {% else %}
        Welcome to <strong>CVBioLabs</strong>! Your {{ user_role|lower }} account has been successfully created by our administration team, and you now have access to our platform.
    {% endif %}
</div>

{% if password %}
<div class="content-text">
    To access your account, please use the following login credentials:
</div>

<div class="info-box" style="text-align: center; background: linear-gradient(135deg, #fff3cd 0%, #fef9e7 100%); border: 2px solid #f47c20; border-radius: 12px;">
    <div style="font-size: 14px; color: #002f6c; margin-bottom: 10px; font-weight: 600;">
        Your Login Credentials
    </div>
    <div style="font-size: 16px; color: #856404; margin-bottom: 10px;">
        <strong>Email:</strong> {{ user_email }}
    </div>
    <div style="font-size: 24px; font-weight: bold; color: #f47c20; letter-spacing: 2px; font-family: 'Courier New', monospace; margin: 15px 0;">
        {{ password }}
    </div>
    <div style="font-size: 12px; color: #666; margin-top: 10px;">
        <i class="fas fa-shield-alt"></i> Please change your password after first login
    </div>
</div>

<div class="warning-box">
    <div style="font-weight: 600; margin-bottom: 10px;">
        <i class="fas fa-exclamation-triangle"></i> Important Security Notice
    </div>
    <div style="font-size: 14px;">
        For your account security, please change your password immediately after your first login. Never share your login credentials with anyone.
    </div>
</div>
{% endif %}

<div class="content-text">
    You can now access your account using the login button below:
</div>

<div style="text-align: center; margin: 30px 0;">
    <a href="{{ login_url }}" class="btn" style="font-size: 18px; padding: 18px 40px;">
        <i class="fas fa-sign-in-alt"></i> Access Your Account
    </a>
</div>

<div class="content-text">
    <strong>What you can do with your {{ user_role }} account:</strong>
</div>

<div class="info-box">
    {% if user_role == 'Doctor' %}
    <ul style="padding-left: 20px; color: #333; margin: 0;">
        <li><strong>Patient Management:</strong> Access patient records, test results, and medical history</li>
        <li><strong>Report Analysis:</strong> Review and analyze diagnostic test reports</li>
        <li><strong>Appointment Management:</strong> Schedule and manage patient consultations</li>
        <li><strong>Prescription Management:</strong> Create and manage patient prescriptions</li>
    </ul>
    {% elif user_role == 'Pickup Agent' %}
    <ul style="padding-left: 20px; color: #333; margin: 0;">
        <li><strong>Route Management:</strong> View and manage your daily pickup routes</li>
        <li><strong>Sample Collection:</strong> Track and manage sample collections</li>
        <li><strong>Mobile App Access:</strong> Use our mobile app for field operations</li>
        <li><strong>Task Management:</strong> Track your daily tasks and assignments</li>
    </ul>
    {% else %}
    <ul style="padding-left: 20px; color: #333; margin: 0;">
        <li><strong>Dashboard Access:</strong> Access your personalized dashboard and tools</li>
        <li><strong>User Management:</strong> Manage users and system settings</li>
        <li><strong>Reports & Analytics:</strong> View comprehensive reports and analytics</li>
        <li><strong>System Configuration:</strong> Configure system settings and preferences</li>
    </ul>
    {% endif %}
</div>

<div class="content-text">
    <strong>Important Information:</strong>
</div>

<div class="content-text">
    <ul style="padding-left: 20px; color: #333;">
        {% if password %}
        <li><strong>Security:</strong> Please change your password immediately after your first login</li>
        {% endif %}
        <li><strong>Professional ID:</strong> Use your Professional ID ({{ professional_id }}) for all official communications</li>
        {% if user_role == 'Doctor' %}
        <li><strong>Compliance:</strong> Ensure all patient data handling follows HIPAA and local privacy regulations</li>
        {% elif user_role == 'Pickup Agent' %}
        <li><strong>Mobile App:</strong> Download our mobile app for efficient field operations</li>
        {% endif %}
    </ul>
</div>

<div class="content-text">
    <strong>Need Help?</strong> Our support team is here to assist you:
</div>

<div class="content-text">
    <ul style="padding-left: 20px; color: #333;">
        <li><strong>Email:</strong> <a href="mailto:<EMAIL>" style="color: #f47c20; text-decoration: none;"><EMAIL></a></li>
        <li><strong>Phone:</strong> +91 78936 20683 (Available 24/7)</li>
        <li><strong>WhatsApp:</strong> +91 78936 20683</li>
    </ul>
</div>

<div class="content-text" style="margin-top: 30px;">
    {% if user_role == 'Doctor' %}
    We're honored to have you as part of our medical team. Together, we'll continue to provide exceptional diagnostic services and improve patient care.
    {% elif user_role == 'Pickup Agent' %}
    Thank you for joining our field operations team. Your role is crucial in ensuring our patients receive timely and professional service.
    {% else %}
    We're excited to have you as part of the CVBioLabs team. Your contribution will help us continue to provide excellent healthcare services.
    {% endif %}
</div>

<div class="content-text" style="color: #002f6c; font-weight: 600;">
    Welcome to the team!<br>
    The CVBioLabs Administration Team
</div>
{% endblock %}
